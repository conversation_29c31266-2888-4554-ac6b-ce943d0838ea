import React from "react";
import { But<PERSON> } from "../components/ui/button";
import { secondaryFont } from "@/constants/fonts";
import Link from "next/link";
import Image from "next/image";

const Hero = () => {
  return (
    <section
      className="relative bg-cover bg-no-repeat bg-center w-full min-h-[32rem] sm:min-h-[36rem] md:h-[30rem] px-4 sm:px-5 py-8 sm:py-12"
      aria-labelledby="hero-heading"
      role="banner"
    >
      {/* Background Image with proper alt text for SEO */}
      <div className="absolute inset-0">
        <Image
          src="/assets/backgrounds/home-bg.png"
          alt="Traditional Chinioti wooden furniture craftsman creating handcrafted furniture in Chiniot, Punjab, Pakistan workshop"
          fill
          className="object-cover"
          priority
          sizes="100vw"
        />

      </div>

      <div className="relative flex flex-col justify-center items-start h-full gap-6 sm:gap-8 md:gap-10 z-10">
        <header>
          <h1
            id="hero-heading"
            className={
              secondaryFont +
              " text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-semibold font-secondary text-white leading-tight drop-shadow-lg"
            }
          >
            Discover the World of{" "}
            <span className="text-accent">Authentic Chinioti</span>
            <br />
            <span className="text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl">
              Handcrafted Wooden Furniture
            </span>
          </h1>
          <p className="text-white text-lg md:text-xl mt-4 max-w-2xl">
            Premium handcrafted wooden furniture from Chiniot, Punjab, Pakistan.
            500+ years of traditional craftsmanship meets modern design.
            Authentic beds, tables, chairs, and custom furniture with worldwide shipping.
          </p>
        </header>

        <div className="flex flex-col sm:flex-row gap-4">
          <Link href="/products" aria-label="Browse our collection of handcrafted Chiniot furniture">
            <Button
              color="dark"
              className="bg-accent hover:bg-accent/90 text-white px-8 py-3 text-lg"
            >
              Explore Our Collection →
            </Button>
          </Link>
          <Link href="/contact" aria-label="Contact us for custom furniture orders">
            <Button
              variant="outline"
              className="border-white text-white hover:bg-white hover:text-black px-8 py-3 text-lg"
            >
              Custom Orders
            </Button>
          </Link>
        </div>

        {/* Trust indicators */}
        <div className="flex flex-wrap gap-6 text-white text-sm">
          <div className="flex items-center gap-2">
            <span className="text-accent">✓</span>
            <span>500+ Years Heritage</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-accent">✓</span>
            <span>Worldwide Shipping</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-accent">✓</span>
            <span>Authentic Craftsmanship</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-accent">✓</span>
            <span>Made in Chiniot, Pakistan</span>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
